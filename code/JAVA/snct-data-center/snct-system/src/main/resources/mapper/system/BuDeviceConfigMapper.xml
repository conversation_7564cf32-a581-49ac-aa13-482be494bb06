<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.BuDeviceConfigMapper">
    
    <resultMap type="BuDeviceConfig" id="BuDeviceConfigResult">
        <result property="id"    column="id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="sn"    column="sn"    />
        <result property="name"    column="name"    />
        <result property="configKey"    column="config_key"    />
        <result property="configValue"    column="config_value"    />
        <result property="enable"    column="enable"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />

        <result property="deptName"    column="dept_name"    />
        <result property="shipName"    column="ship_name"    />
        <result property="deviceName"    column="device_name"    />
        <result property="dictLabel"    column="dict_label"    />
    </resultMap>

    <sql id="selectBuDeviceConfigVo">
        select c.id, c.device_id, c.dept_id, c.sn, c.name, c.config_key, c.config_value, c.enable, c.create_by,
               c.create_time, c.update_by, c.update_time, c.remark,d.dept_name,
               ss.name as ship_name,de.name as device_name,dd.dict_label
        from bu_device_config c
        INNER join sys_dept d on c.dept_id = d.dept_id
        INNER join bu_ship ss on c.sn = ss.sn
        left join bu_device de on c.device_id = de.id
        INNER join sys_dict_data dd on c.config_key = dd.dict_value
    </sql>

    <select id="selectBuDeviceConfigList" parameterType="BuDeviceConfig" resultMap="BuDeviceConfigResult">
        <include refid="selectBuDeviceConfigVo"/>
        <where>  
            <if test="deviceId != null "> and c.device_id = #{deviceId}</if>
            <if test="deptId != null "> and c.dept_id = #{deptId}</if>
            <if test="sn != null  and sn != ''"> and c.sn = #{sn}</if>
            <if test="name != null  and name != ''"> and c.name like concat('%', #{name}, '%')</if>
            <if test="configKey != null  and configKey != ''"> and c.config_key = #{configKey}</if>
            <if test="configValue != null "> and c.config_value = #{configValue}</if>
            <if test="enable != null "> and c.enable = #{enable}</if>
        </where>
    </select>

    <select id="selectBydeviceIdAndKey" parameterType="BuDeviceConfig" resultMap="BuDeviceConfigResult">
        <include refid="selectBuDeviceConfigVo"/>
        <where>
            <if test="deviceId != null "> and c.device_id = #{deviceId}</if>
            <if test="configKey != null  and configKey != ''"> and c.config_key = #{configKey}</if>
        </where>
        and enable = 1 limit 1
    </select>
    
    <select id="selectBuDeviceConfigById" parameterType="Long" resultMap="BuDeviceConfigResult">
        <include refid="selectBuDeviceConfigVo"/>
        where c.id = #{id}
    </select>



    <insert id="insertBuDeviceConfig" parameterType="BuDeviceConfig" useGeneratedKeys="true" keyProperty="id">
        insert into bu_device_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">device_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="sn != null and sn != ''">sn,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="configKey != null and configKey != ''">config_key,</if>
            <if test="configValue != null">config_value,</if>
            <if test="enable != null">enable,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">#{deviceId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="sn != null and sn != ''">#{sn},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="configKey != null and configKey != ''">#{configKey},</if>
            <if test="configValue != null">#{configValue},</if>
            <if test="enable != null">#{enable},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateBuDeviceConfig" parameterType="BuDeviceConfig">
        update bu_device_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="sn != null and sn != ''">sn = #{sn},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="configKey != null and configKey != ''">config_key = #{configKey},</if>
            <if test="configValue != null">config_value = #{configValue},</if>
            <if test="enable != null">enable = #{enable},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBuDeviceConfigById" parameterType="Long">
        delete from bu_device_config where id = #{id}
    </delete>

    <delete id="deleteBuDeviceConfigByIds" parameterType="String">
        delete from bu_device_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>